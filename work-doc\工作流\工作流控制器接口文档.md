# 工作流控制器接口文档

本文档详细介绍了工作流模块中4个控制器的所有接口，包括请求方式、参数、返回值和权限要求。

## 目录
1. [工作流模板节点控制器 (WorkflowTemplateNodeController)](#工作流模板节点控制器)
2. [工作流模板控制器 (WorkflowTemplateController)](#工作流模板控制器)
3. [工作流控制器 (WorkflowController)](#工作流控制器)
4. [工作流模板分组控制器 (WorkflowTemplateGroupController)](#工作流模板分组控制器)

---

## 工作流模板节点控制器

**基础路径**: `/workflow/templateNode`

### 1. 查询工作流模板节点列表
```http
GET /workflow/templateNode/list?pageNum=1&pageSize=10&templateId=1&taskType=11
Authorization: Bearer {token}
```

**请求参数**:
- `pageNum` (Integer): 页码，默认1
- `pageSize` (Integer): 每页数量，默认10
- `templateId` (Long): 模板ID（可选）
- `taskType` (Integer): 任务类型（可选）
- `nodeOrder` (Integer): 节点顺序（可选）

**期望响应**:
```json
{
    "code": 200,
    "msg": "查询成功",
    "rows": [
        {
            "id": 1,
            "templateId": 1,
            "taskType": 11,
            "nodeOrder": 1,
            "nodeParams": "{\"quality\":\"high\",\"algorithm\":\"auto\"}",
            "createTime": "2025-01-25 10:00:00"
        }
    ],
    "total": 1
}
```

### 2. 根据模板ID查询节点列表
```http
GET /workflow/templateNode/listByTemplateId/1
Authorization: Bearer {token}
```

**请求参数**:
- `templateId` (Long): 模板ID

**期望响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        {
            "id": 1,
            "templateId": 1,
            "taskType": 11,
            "nodeOrder": 1,
            "nodeParams": "{\"quality\":\"high\",\"algorithm\":\"auto\"}"
        },
        {
            "id": 2,
            "templateId": 1,
            "taskType": 17,
            "nodeOrder": 2,
            "nodeParams": "{\"threshold\":0.8,\"categories\":[\"adult\",\"violence\"]}"
        }
    ]
}
```

### 3. 导出工作流模板节点列表
```http
POST /workflow/templateNode/export
Authorization: Bearer {token}
Content-Type: application/json

{
    "templateId": 1,
    "taskType": 11
}
```

**返回数据**: Excel文件下载

### 4. 获取工作流模板节点详细信息
```http
GET /workflow/templateNode/1
Authorization: Bearer {token}
```

**请求参数**:
- `id` (Long): 节点ID

**期望响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "id": 1,
        "templateId": 1,
        "taskType": 11,
        "nodeOrder": 1,
        "nodeParams": "{\"quality\":\"high\",\"algorithm\":\"auto\"}",
        "createTime": "2025-01-25 10:00:00",
        "updateTime": "2025-01-25 10:00:00"
    }
}
```

### 5. 新增工作流模板节点
```http
POST /workflow/templateNode
Authorization: Bearer {token}
Content-Type: application/json

{
    "templateId": 1,
    "taskType": 11,
    "nodeOrder": 1,
    "nodeParams": "{\"quality\":\"high\",\"algorithm\":\"auto\"}"
}
```

**期望响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

### 6. 批量新增工作流模板节点
```http
POST /workflow/templateNode/batch
Authorization: Bearer {token}
Content-Type: application/json

[
    {
        "templateId": 1,
        "taskType": 6,
        "nodeOrder": 1,
        "nodeParams": "{\"text\":\"商品展示\",\"style\":\"modern\"}"
    },
    {
        "templateId": 1,
        "taskType": 12,
        "nodeOrder": 2,
        "nodeParams": "{\"enhanceLevel\":\"high\"}"
    }
]
```

**期望响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

### 7. 修改工作流模板节点
```http
PUT /workflow/templateNode
Authorization: Bearer {token}
Content-Type: application/json

{
    "id": 1,
    "templateId": 1,
    "taskType": 11,
    "nodeOrder": 1,
    "nodeParams": "{\"quality\":\"ultra\",\"algorithm\":\"advanced\"}"
}
```

**期望响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

### 8. 删除工作流模板节点
```http
DELETE /workflow/templateNode/1,2,3
Authorization: Bearer {token}
```

**请求参数**:
- `ids` (Long[]): 节点ID数组，用逗号分隔

**期望响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

### 9. 根据模板ID删除所有节点
```http
DELETE /workflow/templateNode/template/1
Authorization: Bearer {token}
```

**请求参数**:
- `templateId` (Long): 模板ID

**期望响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

### 10. 统计模板中指定任务类型的节点数量
```http
GET /workflow/templateNode/count/1/11
Authorization: Bearer {token}
```

**请求参数**:
- `templateId` (Long): 模板ID
- `taskType` (Integer): 任务类型

**期望响应**:
```json
{
    "code": 200,
    "msg": "查询成功",
    "data": 1
}
```

---

## 工作流模板控制器

**基础路径**: `/workflow/template`
**Swagger标签**: `工作流模板管理`

### 1. 查询工作流模板列表
```http
GET /workflow/template/list?pageNum=1&pageSize=10&teamId=1&groupId=1&templateName=图片处理
Authorization: Bearer {token}
```

**请求参数**:
- `pageNum` (Integer): 页码，默认1
- `pageSize` (Integer): 每页数量，默认10
- `teamId` (Long): 团队ID（可选）
- `groupId` (Long): 分组ID（可选）
- `templateName` (String): 模板名称（可选，模糊查询）

**期望响应**:
```json
{
    "code": 200,
    "msg": "查询成功",
    "rows": [
        {
            "id": 1,
            "templateName": "完整图片处理流水线",
            "groupId": 1,
            "groupName": "图片处理流水线",
            "teamId": 1,
            "userId": 1001,
            "createTime": "2025-01-25 10:00:00"
        }
    ],
    "total": 1
}
```

### 2. 导出工作流模板列表
```http
POST /workflow/template/export
Authorization: Bearer {token}
Content-Type: application/json

{
    "teamId": 1,
    "groupId": 1,
    "templateName": "图片处理"
}
```

**返回数据**: Excel文件下载

### 3. 获取工作流模板详细信息
```http
GET /workflow/template/detail/1
Authorization: Bearer {token}
```

**请求参数**:
- `id` (Long): 模板ID

**期望响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "id": 1,
        "templateName": "完整图片处理流水线",
        "groupId": 1,
        "groupName": "图片处理流水线",
        "teamId": 1,
        "userId": 1001,
        "createTime": "2025-01-25 10:00:00",
        "updateTime": "2025-01-25 10:00:00"
    }
}
```

### 4. 获取工作流模板详细信息（包含节点）
```http
GET /workflow/template/details/1
Authorization: Bearer {token}
```

**请求参数**:
- `id` (Long): 模板ID

**期望响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "id": 1,
        "templateName": "完整图片处理流水线",
        "groupId": 1,
        "groupName": "图片处理流水线",
        "teamId": 1,
        "userId": 1001,
        "nodeList": [
            {
                "id": 1,
                "templateId": 1,
                "taskType": 11,
                "nodeOrder": 1,
                "nodeParams": "{\"quality\":\"high\",\"algorithm\":\"auto\"}"
            },
            {
                "id": 2,
                "templateId": 1,
                "taskType": 17,
                "nodeOrder": 2,
                "nodeParams": "{\"threshold\":0.8,\"categories\":[\"adult\",\"violence\"]}"
            }
        ]
    }
}
```

### 5. 新增工作流模板
```http
POST /workflow/template
Authorization: Bearer {token}
Content-Type: application/json

{
    "templateName": "基础图片处理",
    "groupId": 1,
    "teamId": 1,
    "userId": 1001
}
```

**期望响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

### 6. 新增工作流模板（包含节点）
```http
POST /workflow/template/withNodes
Authorization: Bearer {token}
Content-Type: application/json

{
    "templateName": "完整图片处理流水线",
    "groupId": 1,
    "teamId": 1,
    "userId": 1001,
    "nodeList": [
        {
            "taskType": 11,
            "nodeOrder": 1,
            "nodeParams": "{\"quality\":\"high\",\"algorithm\":\"auto\"}"
        },
        {
            "taskType": 17,
            "nodeOrder": 2,
            "nodeParams": "{\"threshold\":0.8,\"categories\":[\"adult\",\"violence\"]}"
        },
        {
            "taskType": 18,
            "nodeOrder": 3,
            "nodeParams": "{\"language\":\"zh-CN\",\"maxLength\":50}"
        }
    ]
}
```

**期望响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

### 7. 修改工作流模板
```http
PUT /workflow/template
Authorization: Bearer {token}
Content-Type: application/json

{
    "id": 1,
    "templateName": "高级图片处理流水线",
    "groupId": 1,
    "teamId": 1,
    "userId": 1001
}
```

**期望响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

### 8. 更新模板节点
```http
PUT /workflow/template/nodes/1
Authorization: Bearer {token}
Content-Type: application/json

[
    {
        "taskType": 11,
        "nodeOrder": 1,
        "nodeParams": "{\"quality\":\"ultra\",\"algorithm\":\"auto\"}"
    },
    {
        "taskType": 9,
        "nodeOrder": 2,
        "nodeParams": "{\"splitCount\":4,\"similarity\":0.9}"
    },
    {
        "taskType": 17,
        "nodeOrder": 3,
        "nodeParams": "{\"threshold\":0.9,\"categories\":[\"adult\",\"violence\",\"drugs\"]}"
    },
    {
        "taskType": 18,
        "nodeOrder": 4,
        "nodeParams": "{\"language\":\"zh-CN\",\"maxLength\":100}"
    }
]
```

**请求参数**:
- `templateId` (Long): 模板ID（URL路径参数）

**期望响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

### 9. 删除工作流模板
```http
DELETE /workflow/template/delete/1,2,3
Authorization: Bearer {token}
```

**请求参数**:
- `ids` (Long[]): 模板ID数组，用逗号分隔

**期望响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

### 10. 复制工作流模板
```http
POST /workflow/template/copy/1
Authorization: Bearer {token}
Content-Type: application/json

{
    "newTemplateName": "完整图片处理流水线_副本",
    "newGroupId": 1,
    "teamId": 1,
    "userId": 1001
}
```

**请求参数**:
- `templateId` (Long): 源模板ID（URL路径参数）

**期望响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

### 11. 移动模板到指定分组
```http
PUT /workflow/template/move/1
Authorization: Bearer {token}
Content-Type: application/json

{
    "newGroupId": 2
}
```

**请求参数**:
- `templateId` (Long): 模板ID（URL路径参数）

**期望响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

### 12. 检查模板名称唯一性
```http
POST /workflow/template/checkTemplateNameUnique
Authorization: Bearer {token}
Content-Type: application/json

{
    "templateName": "新模板名称",
    "teamId": 1,
    "id": null
}
```

**期望响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": true
}
```

### 13. 验证节点配置
```http
POST /workflow/template/validateNodes
Authorization: Bearer {token}
Content-Type: application/json

[
    {
        "taskType": 11,
        "nodeOrder": 1
    },
    {
        "taskType": 17,
        "nodeOrder": 2
    },
    {
        "taskType": 18,
        "nodeOrder": 3
    }
]
```

**期望响应（成功）**:
```json
{
    "code": 200,
    "msg": "节点配置验证通过",
    "data": null
}
```

**期望响应（失败）**:
```json
{
    "code": 500,
    "msg": "任务类型18（标题提取）只能作为最后一个节点"
}
```

---

## 工作流控制器

**基础路径**: `/workflow/workflow`
**Swagger标签**: `工作流管理`

### 1. 查询工作流列表
```http
GET /workflow/workflow/list?pageNum=1&pageSize=10&teamId=1&status=1&workflowName=批量处理
Authorization: Bearer {token}
```

**请求参数**:
- `pageNum` (Integer): 页码，默认1
- `pageSize` (Integer): 每页数量，默认10
- `teamId` (Long): 团队ID（可选）
- `templateId` (Long): 模板ID（可选）
- `status` (Integer): 状态（可选，0-待执行，1-执行中，2-已完成，3-已取消，4-执行失败）
- `workflowName` (String): 工作流名称（可选，模糊查询）
- `userId` (Long): 用户ID（可选）

**期望响应**:
```json
{
    "code": 200,
    "msg": "查询成功",
    "rows": [
        {
            "id": 1,
            "workflowName": "批量图片处理_20250125_002",
            "templateId": 1,
            "templateName": "完整图片处理流水线",
            "status": 1,
            "statusDesc": "执行中",
            "currentNodeOrder": 2,
            "totalNodes": 3,
            "teamId": 1,
            "userId": 1001,
            "createTime": "2025-01-25 10:30:00"
        }
    ],
    "total": 1
}
```

### 2. 导出工作流列表
```http
POST /workflow/workflow/export
Authorization: Bearer {token}
Content-Type: application/json

{
    "teamId": 1,
    "status": 2,
    "workflowName": "批量处理"
}
```

**返回数据**: Excel文件下载

### 3. 获取工作流详细信息
```http
GET /workflow/workflow/1
Authorization: Bearer {token}
```

**请求参数**:
- `id` (Long): 工作流ID

**期望响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "id": 1,
        "workflowName": "批量图片处理_20250125_002",
        "templateId": 1,
        "templateName": "完整图片处理流水线",
        "status": 1,
        "statusDesc": "执行中",
        "currentNodeOrder": 2,
        "totalNodes": 3,
        "teamId": 1,
        "userId": 1001,
        "createTime": "2025-01-25 10:30:00",
        "startTime": "2025-01-25 10:30:00"
    }
}
```

### 4. 获取工作流详细信息（包含素材和节点执行记录）
```http
GET /workflow/workflow/details/1
Authorization: Bearer {token}
```

**请求参数**:
- `id` (Long): 工作流ID

**期望响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "id": 1,
        "workflowName": "批量图片处理_20250125_002",
        "templateId": 1,
        "templateName": "完整图片处理流水线",
        "currentNodeOrder": 1,
        "totalNodes": 3,
        "status": 0,
        "teamId": 1,
        "userId": 1001,
        "materialList": [
            {
                "id": 1,
                "workflowId": 1,
                "materialUrl": "/upload/2025/01/25/image1_20250125001.jpg",
                "sortOrder": 1
            },
            {
                "id": 2,
                "workflowId": 1,
                "materialUrl": "/upload/2025/01/25/image2_20250125002.jpg",
                "sortOrder": 2
            }
        ],
        "nodeExecutionList": [
            {
                "id": 1,
                "workflowId": 1,
                "nodeOrder": 1,
                "taskType": 11,
                "taskId": null,
                "status": 0
            },
            {
                "id": 2,
                "workflowId": 1,
                "nodeOrder": 2,
                "taskType": 17,
                "taskId": null,
                "status": 0
            }
        ]
    }
}
```

### 5. 新增工作流
```http
POST /workflow/workflow
Authorization: Bearer {token}
Content-Type: application/json

{
    "workflowName": "批量图片处理_20250125_001",
    "templateId": 1,
    "teamId": 1,
    "userId": 1001
}
```

**期望响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

### 6. 创建工作流（通过文件上传）
```http
POST /workflow/workflow/createByFiles
Authorization: Bearer {token}
Content-Type: multipart/form-data

workflowName=批量图片处理_20250125_002
templateId=1
teamId=1
userId=1001
files=@image1.jpg
files=@image2.jpg
files=@image3.jpg
```

**请求参数**:
- `workflowName` (String): 工作流名称
- `templateId` (Long): 模板ID
- `teamId` (Long): 团队ID
- `userId` (Long): 用户ID（可选）
- `files` (List<MultipartFile>): 素材文件列表
- `files[]` (List<MultipartFile>): 素材文件数组（备用参数）

**期望响应**:
```json
{
    "code": 200,
    "msg": "创建工作流成功",
    "data": {
        "workflowId": 1,
        "workflowName": "批量图片处理_20250125_002",
        "totalFiles": 3
    }
}
```

### 7. 创建工作流（通过URL列表）
```http
POST /workflow/workflow/createByUrls
Authorization: Bearer {token}
Content-Type: application/json

{
    "workflowName": "批量图片处理_20250125_003",
    "templateId": 1,
    "teamId": 1,
    "userId": 1001,
    "materialUrls": [
        "https://example.com/image1.jpg",
        "https://example.com/image2.jpg",
        "https://example.com/image3.jpg"
    ]
}
```

**请求参数**:
- `workflowName` (String): 工作流名称
- `templateId` (Long): 模板ID
- `teamId` (Long): 团队ID（可选）
- `userId` (Long): 用户ID（可选）
- `materialUrls` (List<String>): 素材URL列表

**期望响应**:
```json
{
    "code": 200,
    "msg": "创建工作流成功",
    "data": {
        "workflowId": 2,
        "workflowName": "批量图片处理_20250125_003",
        "totalUrls": 3
    }
}
```

### 8. 修改工作流
```http
PUT /workflow/workflow
Authorization: Bearer {token}
Content-Type: application/json

{
    "id": 1,
    "workflowName": "批量图片处理_20250125_001_修改版",
    "templateId": 1,
    "teamId": 1,
    "userId": 1001
}
```

**期望响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

### 9. 删除工作流
```http
DELETE /workflow/workflow/1,2,3
Authorization: Bearer {token}
```

**请求参数**:
- `ids` (Long[]): 工作流ID数组，用逗号分隔

**期望响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

### 10. 启动工作流执行
```http
POST /workflow/workflow/start/1
Authorization: Bearer {token}
```

**请求参数**:
- `id` (Long): 工作流ID（URL路径参数）

**期望响应**:
```json
{
    "code": 200,
    "msg": "工作流启动成功",
    "data": null
}
```

### 11. 取消工作流执行
```http
POST /workflow/workflow/cancel/1
Authorization: Bearer {token}
```

**请求参数**:
- `id` (Long): 工作流ID（URL路径参数）

**期望响应**:
```json
{
    "code": 200,
    "msg": "工作流取消成功",
    "data": null
}
```

### 12. 获取工作流执行进度
```http
GET /workflow/workflow/progress/1
Authorization: Bearer {token}
```

**请求参数**:
- `id` (Long): 工作流ID（URL路径参数）

**期望响应（执行中）**:
```json
{
    "code": 200,
    "msg": "查询成功",
    "data": {
        "workflowId": 1,
        "workflowName": "批量图片处理_20250125_002",
        "status": 1,
        "statusDesc": "执行中",
        "currentNodeOrder": 2,
        "totalNodes": 3,
        "progress": 33.33,
        "startTime": "2025-01-25 10:30:00",
        "nodeExecutions": [
            {
                "nodeOrder": 1,
                "taskType": 11,
                "taskTypeDesc": "图片去背景",
                "status": 2,
                "statusDesc": "已完成",
                "taskId": 10001,
                "startTime": "2025-01-25 10:30:00",
                "endTime": "2025-01-25 10:32:15"
            },
            {
                "nodeOrder": 2,
                "taskType": 17,
                "taskTypeDesc": "侵权风险过滤",
                "status": 1,
                "statusDesc": "执行中",
                "taskId": 10002,
                "startTime": "2025-01-25 10:32:20",
                "endTime": null
            },
            {
                "nodeOrder": 3,
                "taskType": 18,
                "taskTypeDesc": "标题提取",
                "status": 0,
                "statusDesc": "待执行",
                "taskId": null,
                "startTime": null,
                "endTime": null
            }
        ]
    }
}
```

### 13. 处理工作流节点执行完成回调
```http
POST /workflow/workflow/nodeCallback
Authorization: Bearer {token}
Content-Type: application/json

{
    "nodeExecutionId": 1,
    "success": true,
    "resultData": {
        "processedFiles": [
            "/upload/processed/image1_processed.jpg",
            "/upload/processed/image2_processed.jpg"
        ],
        "processingTime": 135,
        "quality": "high"
    }
}
```

**请求参数**:
- `nodeExecutionId` (Long): 节点执行记录ID
- `success` (Boolean): 是否执行成功
- `resultData` (Object): 结果数据（可选）

**期望响应**:
```json
{
    "code": 200,
    "msg": "回调处理成功",
    "data": null
}
```

---

## 工作流模板分组控制器

**基础路径**: `/workflow/templateGroup`
**Swagger标签**: `工作流模板分组管理`

### 1. 查询工作流模板分组列表
```http
GET /workflow/templateGroup/list?pageNum=1&pageSize=10&teamId=1&groupName=图片处理
Authorization: Bearer {token}
```

**请求参数**:
- `pageNum` (Integer): 页码，默认1
- `pageSize` (Integer): 每页数量，默认10
- `teamId` (Long): 团队ID（可选）
- `groupName` (String): 分组名称（可选，模糊查询）
- `userId` (Long): 用户ID（可选）

**期望响应**:
```json
{
    "code": 200,
    "msg": "查询成功",
    "rows": [
        {
            "id": 1,
            "groupName": "图片处理流水线",
            "teamId": 1,
            "userId": 1001,
            "templateCount": 3,
            "createTime": "2025-01-25 10:00:00",
            "updateTime": "2025-01-25 10:00:00"
        }
    ],
    "total": 1
}
```

### 2. 导出工作流模板分组列表
```http
POST /workflow/templateGroup/export
Authorization: Bearer {token}
Content-Type: application/json

{
    "teamId": 1,
    "groupName": "图片处理"
}
```

**返回数据**: Excel文件下载

### 3. 获取工作流模板分组详细信息
```http
GET /workflow/templateGroup/1
Authorization: Bearer {token}
```

**请求参数**:
- `id` (Long): 分组ID

**期望响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "id": 1,
        "groupName": "图片处理流水线",
        "teamId": 1,
        "userId": 1001,
        "templateCount": 3,
        "createTime": "2025-01-25 10:00:00",
        "updateTime": "2025-01-25 10:00:00"
    }
}
```

### 4. 新增工作流模板分组
```http
POST /workflow/templateGroup
Authorization: Bearer {token}
Content-Type: application/json

{
    "groupName": "图片处理流水线",
    "teamId": 1,
    "userId": 1001
}
```

**期望响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

### 5. 修改工作流模板分组
```http
PUT /workflow/templateGroup
Authorization: Bearer {token}
Content-Type: application/json

{
    "id": 1,
    "groupName": "高级图片处理流水线",
    "teamId": 1,
    "userId": 1001
}
```

**期望响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

### 6. 删除工作流模板分组
```http
DELETE /workflow/templateGroup/1,2,3
Authorization: Bearer {token}
```

**请求参数**:
- `ids` (Long[]): 分组ID数组，用逗号分隔

**期望响应（成功）**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

**期望响应（失败-分组下有模板）**:
```json
{
    "code": 500,
    "msg": "分组下还有模板，无法删除"
}
```

### 7. 检查分组名称唯一性
```http
POST /workflow/templateGroup/checkGroupNameUnique
Authorization: Bearer {token}
Content-Type: application/json

{
    "groupName": "新分组名称",
    "teamId": 1,
    "id": null
}
```

**期望响应（唯一）**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": true
}
```

**期望响应（不唯一）**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": false
}
```

---

## 通用说明

### 认证方式
所有接口都需要在请求头中携带Bearer Token：
```http
Authorization: Bearer {your_access_token}
```

### 内容类型
- **JSON请求**: `Content-Type: application/json`
- **文件上传**: `Content-Type: multipart/form-data`
- **表单请求**: `Content-Type: application/x-www-form-urlencoded`

### 权限系统
所有权限控制使用 `@PreAuthorize` 注解，权限格式为 `模块:功能:操作`，例如：
- `workflow:template:list` - 工作流模板列表查询权限
- `workflow:workflow:start` - 工作流启动权限

### 返回数据格式
- **AjaxResult**: 标准Ajax响应格式
  ```json
  {
      "code": 200,
      "msg": "操作成功",
      "data": null
  }
  ```
- **TableDataInfo**: 分页数据格式
  ```json
  {
      "code": 200,
      "msg": "查询成功",
      "rows": [],
      "total": 0
  }
  ```

### 状态码说明
- **200**: 成功
- **400**: 请求参数错误
- **401**: 未认证
- **403**: 无权限
- **500**: 服务器内部错误

### 工作流状态
- **0**: 待执行
- **1**: 执行中
- **2**: 已完成
- **3**: 已取消
- **4**: 执行失败

### 节点执行状态
- **0**: 待执行
- **1**: 执行中
- **2**: 已完成
- **3**: 执行失败

### 日志记录
大部分操作接口都使用 `@Log` 注解进行操作日志记录，包括：
- INSERT: 新增操作
- UPDATE: 修改操作
- DELETE: 删除操作
- EXPORT: 导出操作

### 错误处理
控制器继承 `BaseController`，提供统一的错误处理和响应格式化功能。

---

**文档生成时间**: 2025-01-26
**文档版本**: v2.0
**维护人员**: dataxai团队